/**
 * @file Binding/BindingLocation.h
 * @brief 属性绑定源代码位置信息
 * 
 * 这个文件提供了属性绑定的源代码位置跟踪功能，
 * 用于调试和错误报告。
 */

#ifndef PROPERTY_SYSTEM_BINDING_LOCATION_H
#define PROPERTY_SYSTEM_BINDING_LOCATION_H

#include <cstdint>
#include <cstdio>

// 检查C++20 source_location支持
#if __has_include(<source_location>) && __cplusplus >= 202002L
#include <source_location>
#if defined(__cpp_lib_source_location)
#define PROPERTY_HAS_SOURCE_LOCATION
#define PROPERTY_SOURCE_LOCATION_NAMESPACE std
#endif
#endif

// 检查实验性source_location支持
#if __has_include(<experimental/source_location>) && !defined(PROPERTY_HAS_SOURCE_LOCATION)
#include <experimental/source_location>
#if defined(__cpp_lib_experimental_source_location)
#define PROPERTY_HAS_SOURCE_LOCATION
#define PROPERTY_SOURCE_LOCATION_NAMESPACE std::experimental
#endif
#endif

namespace PropertySystem::Binding {

// ==================================================================================
// 源代码位置结构
// ==================================================================================

/**
 * @brief 属性绑定的源代码位置信息
 * 
 * 这个结构存储了创建属性绑定时的源代码位置信息，
 * 用于调试和错误报告。
 */
struct PropertyBindingSourceLocation {
    const char *fileName = nullptr;      ///< 文件名
    const char *functionName = nullptr;  ///< 函数名
    std::uint32_t line = 0;              ///< 行号
    std::uint32_t column = 0;            ///< 列号

    // ==================================================================================
    // 构造函数
    // ==================================================================================
    
    /**
     * @brief 默认构造函数
     */
    constexpr PropertyBindingSourceLocation() = default;

    /**
     * @brief 手动构造函数
     * @param file 文件名
     * @param func 函数名
     * @param ln 行号
     * @param col 列号
     */
    constexpr PropertyBindingSourceLocation(const char* file, const char* func, 
                                          std::uint32_t ln, std::uint32_t col)
        : fileName(file), functionName(func), line(ln), column(col) {}

#ifdef PROPERTY_HAS_SOURCE_LOCATION
    /**
     * @brief 从std::source_location构造
     * @param cppLocation C++20标准库的source_location对象
     */
    constexpr PropertyBindingSourceLocation(const PROPERTY_SOURCE_LOCATION_NAMESPACE::source_location &cppLocation)
        : fileName(cppLocation.file_name())
        , functionName(cppLocation.function_name())
        , line(cppLocation.line())
        , column(cppLocation.column()) {}

    /**
     * @brief 从std::source_location创建的工厂函数
     * @param cppLocation C++20标准库的source_location对象
     * @return PropertyBindingSourceLocation对象
     */
    static consteval PropertyBindingSourceLocation
    fromStdSourceLocation(const PROPERTY_SOURCE_LOCATION_NAMESPACE::source_location &cppLocation) {
        return PropertyBindingSourceLocation(cppLocation);
    }
#endif

    // ==================================================================================
    // 查询方法
    // ==================================================================================
    
    /**
     * @brief 检查位置信息是否有效
     * @return 如果有有效的位置信息则返回true
     */
    constexpr bool isValid() const {
        return fileName != nullptr && line > 0;
    }

    /**
     * @brief 获取文件名（不包含路径）
     * @return 文件名字符串，如果无效则返回"<unknown>"
     */
    constexpr const char* file() const {
        if (!fileName) return "<unknown>";
        
        // 查找最后一个路径分隔符
        const char* lastSlash = fileName;
        for (const char* p = fileName; *p; ++p) {
            if (*p == '/' || *p == '\\') {
                lastSlash = p + 1;
            }
        }
        return lastSlash;
    }

    /**
     * @brief 获取函数名
     * @return 函数名字符串，如果无效则返回"<unknown>"
     */
    constexpr const char* function() const {
        return functionName ? functionName : "<unknown>";
    }

    /**
     * @brief 获取完整的位置描述字符串
     * @return 格式化的位置字符串
     */
    const char* toString() const {
        static thread_local char buffer[512];
        if (isValid()) {
            std::snprintf(buffer, sizeof(buffer), "%s:%u in %s()",
                         file(), line, function());
        } else {
            std::snprintf(buffer, sizeof(buffer), "<unknown location>");
        }
        return buffer;
    }

    // ==================================================================================
    // 比较运算符
    // ==================================================================================
    
    /**
     * @brief 相等比较运算符
     * @param other 另一个位置对象
     * @return 如果位置相同则返回true
     */
    constexpr bool operator==(const PropertyBindingSourceLocation& other) const {
        return line == other.line && column == other.column &&
               fileName == other.fileName && functionName == other.functionName;
    }

    /**
     * @brief 不等比较运算符
     * @param other 另一个位置对象
     * @return 如果位置不同则返回true
     */
    constexpr bool operator!=(const PropertyBindingSourceLocation& other) const {
        return !(*this == other);
    }
};

// ==================================================================================
// 便利宏定义
// ==================================================================================

#ifdef PROPERTY_HAS_SOURCE_LOCATION

/**
 * @brief 获取当前源代码位置的宏
 */
#if defined(_MSC_VER)
// MSVC在constexpr中使用source_location时有问题，使用工厂函数作为解决方案
#define PROPERTY_DEFAULT_BINDING_LOCATION \
    PropertySystem::Binding::PropertyBindingSourceLocation::fromStdSourceLocation( \
        PROPERTY_SOURCE_LOCATION_NAMESPACE::source_location::current())
#else
// GCC和Clang可以直接使用
#define PROPERTY_DEFAULT_BINDING_LOCATION \
    PropertySystem::Binding::PropertyBindingSourceLocation( \
        PROPERTY_SOURCE_LOCATION_NAMESPACE::source_location::current())
#endif

/**
 * @brief 手动指定源代码位置的宏
 * @param file 文件名
 * @param line 行号
 * @param func 函数名
 */
#define PROPERTY_BINDING_LOCATION(file, line, func) \
    PropertySystem::Binding::PropertyBindingSourceLocation(file, func, line, 0)

#else

/**
 * @brief 无source_location支持时的默认位置
 */
#define PROPERTY_DEFAULT_BINDING_LOCATION \
    PropertySystem::Binding::PropertyBindingSourceLocation()

/**
 * @brief 手动指定源代码位置的宏（无source_location支持）
 * @param file 文件名
 * @param line 行号
 * @param func 函数名
 */
#define PROPERTY_BINDING_LOCATION(file, line, func) \
    PropertySystem::Binding::PropertyBindingSourceLocation(file, func, line, 0)

#endif

/**
 * @brief 当前位置的便利宏
 */
#define PROPERTY_HERE PROPERTY_BINDING_LOCATION(__FILE__, __LINE__, __FUNCTION__)

// ==================================================================================
// 工具函数
// ==================================================================================

/**
 * @brief 创建无效的源代码位置
 * @return 无效的PropertyBindingSourceLocation对象
 */
constexpr PropertyBindingSourceLocation makeInvalidLocation() {
    return PropertyBindingSourceLocation();
}

/**
 * @brief 创建手动指定的源代码位置
 * @param file 文件名
 * @param func 函数名
 * @param line 行号
 * @param column 列号
 * @return PropertyBindingSourceLocation对象
 */
constexpr PropertyBindingSourceLocation makeLocation(const char* file, const char* func, 
                                                    std::uint32_t line, std::uint32_t column = 0) {
    return PropertyBindingSourceLocation(file, func, line, column);
}

} // namespace PropertySystem::Binding

// ==================================================================================
// 全局命名空间别名（向后兼容）
// ==================================================================================

using PropertySystem::Binding::PropertyBindingSourceLocation;

// 导出便利宏到全局命名空间
#ifndef PROPERTY_DEFAULT_BINDING_LOCATION_GLOBAL
#define PROPERTY_DEFAULT_BINDING_LOCATION_GLOBAL PROPERTY_DEFAULT_BINDING_LOCATION
#endif

#endif // PROPERTY_SYSTEM_BINDING_LOCATION_H
