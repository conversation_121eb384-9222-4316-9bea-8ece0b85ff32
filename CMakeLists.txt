set(MODULE property)
set(MODULE_SRC

    property/property.h
    # property/propertyprivate.h
    # property/property_p.h
    property/property.cpp
    # shareddata.h
    # taggedpointer.h
    # varlengtharray.h
    # bindingstorage.h
)

add_library(${MODULE})

target_sources(${MODULE} PRIVATE ${MODULE_SRC})

target_include_directories(${MODULE} PUBLIC
    ${PROJECT_BINARY_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/property
)

target_link_libraries(${MODULE} PUBLIC global)
