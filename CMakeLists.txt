set(MODULE property)
set(MODULE_SRC
    PropertySystem/PropertySystem.h
    PropertySystem/Core/PropertyTypes.h
    PropertySystem/Core/PropertyData.h
    PropertySystem/Binding/BindingLocation.h
    PropertySystem/Observer/PropertyObserver.h
    PropertySystem/Utility/UpdateGroup.h
)

add_library(${MODULE})

target_sources(${MODULE} PRIVATE ${MODULE_SRC})

target_include_directories(${MODULE} PUBLIC
    ${PROJECT_BINARY_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/PropertySystem
)

target_link_libraries(${MODULE} PUBLIC global)
