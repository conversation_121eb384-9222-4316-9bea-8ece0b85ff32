/**
 * @file modern_binding_vtable.h
 * @brief 现代化的绑定函数表实现，移除MetaType依赖
 * 
 * 这个文件提供了简化的BindingFunctionVTable实现，移除了不必要的MetaType参数，
 * 提高了性能并简化了接口。
 */

#ifndef MODERN_BINDING_VTABLE_H
#define MODERN_BINDING_VTABLE_H

#include <type_traits>
#include <utility>
#include <concepts>
#include <functional>
#include "concepts.h"

namespace PropertyBindings {

// ==================================================================================
// 前向声明
// ==================================================================================

class UntypedPropertyData;

template<typename T>
class PropertyData;

// ==================================================================================
// 现代化的绑定函数表
// ==================================================================================

/**
 * @brief 现代化的绑定函数虚表，移除MetaType依赖
 * 
 * 这个结构提供了一个统一的接口来处理不同类型的可调用对象，
 * 相比原版本移除了不必要的MetaType参数，提高了性能。
 */
struct ModernBindingFunctionVTable {
    /**
     * @brief 调用函数指针类型
     * @param propertyData 属性数据指针
     * @param functor 可调用对象指针
     * @return 是否成功调用并更新了属性值
     */
    using CallFn = bool(*)(UntypedPropertyData *propertyData, void *functor);
    
    /**
     * @brief 析构函数指针类型
     * @param functor 要析构的可调用对象指针
     */
    using DtorFn = void(*)(void *functor);
    
    /**
     * @brief 移动构造函数指针类型
     * @param dest 目标地址
     * @param src 源对象地址
     */
    using MoveCtrFn = void(*)(void *dest, void *src);

    CallFn call;              ///< 调用函数
    DtorFn destroy;           ///< 析构函数
    MoveCtrFn moveConstruct;  ///< 移动构造函数
    size_t size;              ///< 可调用对象大小

    /**
     * @brief 为特定类型创建绑定函数表
     * @tparam Callable 可调用对象类型
     * @tparam PropertyType 属性类型（可选，用于类型化绑定）
     * @return 对应的绑定函数表
     */
    template<typename Callable, typename PropertyType = void>
    static constexpr ModernBindingFunctionVTable createFor() {
        static_assert(alignof(Callable) <= alignof(std::max_align_t), 
                     "Bindings do not support overaligned functors!");
        
        return {
            // 调用函数实现
            /*call=*/[](UntypedPropertyData *dataPtr, void *f) -> bool {
                if constexpr (!std::is_invocable_v<Callable>) {
                    // 处理未类型化的可调用对象
                    static_assert(std::is_same_v<PropertyType, void>, 
                                 "Untyped callable requires void PropertyType");
                    return false;
                } else if constexpr (!std::is_same_v<PropertyType, void>) {
                    // 类型化绑定：调用函数并更新属性值
                    auto *callable = static_cast<Callable *>(f);
                    // 使用reinterpret_cast进行类型转换（假设调用者保证类型正确）
                    auto *propertyPtr = reinterpret_cast<PropertyData<PropertyType> *>(dataPtr);

                    if constexpr (std::is_void_v<std::invoke_result_t<Callable>>) {
                        // 无返回值的函数，不更新属性
                        std::invoke(*callable);
                        return false;
                    } else {
                        // 有返回值的函数，更新属性值
                        auto newValue = std::invoke(*callable);
                        propertyPtr->setValueBypassingBindings(std::move(newValue));
                        return true;
                    }
                } else {
                    // 未指定PropertyType的情况
                    static_assert(std::is_same_v<PropertyType, void>, 
                                 "PropertyType must be specified for typed bindings");
                    return false;
                }
            },
            
            // 析构函数实现
            /*destroy=*/[](void *f) {
                static_cast<Callable *>(f)->~Callable();
            },
            
            // 移动构造函数实现
            /*moveConstruct=*/[](void *dest, void *src) {
                new (dest) Callable(std::move(*static_cast<Callable *>(src)));
            },
            
            // 对象大小
            /*size=*/sizeof(Callable)
        };
    }
};

/**
 * @brief 为特定类型获取绑定函数表的便利模板变量
 * @tparam Callable 可调用对象类型
 * @tparam PropertyType 属性类型
 */
template<typename Callable, typename PropertyType = void>
inline constexpr ModernBindingFunctionVTable modernBindingFunctionVTable = 
    ModernBindingFunctionVTable::createFor<Callable, PropertyType>();

// ==================================================================================
// 绑定函数包装器
// ==================================================================================

/**
 * @brief 现代化的绑定函数包装器
 * 
 * 这个结构将函数表和可调用对象组合在一起，提供统一的调用接口。
 */
struct ModernPropertyBindingFunction {
    const ModernBindingFunctionVTable *vtable;  ///< 函数表指针
    void *functor;                              ///< 可调用对象指针
    
    /**
     * @brief 调用绑定函数
     * @param propertyData 属性数据指针
     * @return 是否成功更新了属性值
     */
    bool operator()(UntypedPropertyData *propertyData) const {
        return vtable->call(propertyData, functor);
    }
    
    /**
     * @brief 检查绑定函数是否有效
     * @return 如果vtable和functor都不为空则返回true
     */
    bool isValid() const {
        return vtable != nullptr && functor != nullptr;
    }
};

// ==================================================================================
// 类型安全的绑定创建函数
// ==================================================================================

/**
 * @brief 创建类型安全的绑定函数
 * @tparam PropertyType 属性类型
 * @tparam Callable 可调用对象类型
 * @param callable 可调用对象
 * @return 绑定函数包装器
 */
template<typename PropertyType, typename Callable>
requires PropertyConcepts::PropertyBindingCallable<Callable, PropertyType>
constexpr ModernPropertyBindingFunction makeTypedBinding(Callable &&callable) {
    return {
        &modernBindingFunctionVTable<std::remove_reference_t<Callable>, PropertyType>,
        &callable
    };
}

/**
 * @brief 创建未类型化的绑定函数
 * @tparam Callable 可调用对象类型
 * @param callable 可调用对象
 * @return 绑定函数包装器
 */
template<typename Callable>
requires std::invocable<Callable>
constexpr ModernPropertyBindingFunction makeUntypedBinding(Callable &&callable) {
    return {
        &modernBindingFunctionVTable<std::remove_reference_t<Callable>, void>,
        &callable
    };
}

} // namespace PropertyBindings

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertyBindings::ModernBindingFunctionVTable;
using PropertyBindings::ModernPropertyBindingFunction;
using PropertyBindings::modernBindingFunctionVTable;
using PropertyBindings::makeTypedBinding;
using PropertyBindings::makeUntypedBinding;

#endif // MODERN_BINDING_VTABLE_H
