#ifndef MODERN_CONTAINER_H
#define MODERN_CONTAINER_H

#include <vector>
#include <array>
#include <memory>
#include <algorithm>
#include <iterator>
#include <type_traits>
#include <initializer_list>
#include "concepts.h"

/**
 * @file modern_container.h
 * @brief Modern C++20 container implementation using std::vector with small object optimization
 * 
 * This file provides a modern replacement for the custom VarLengthArray
 * implementation, using std::vector as the underlying mechanism while
 * maintaining small object optimization and API compatibility.
 */

namespace PropertyContainers {

// ==================================================================================
// Small Object Optimized Vector
// ==================================================================================

/**
 * @brief Modern replacement for VarLengthArray with small object optimization
 * @tparam T Element type
 * @tparam InlineCapacity Number of elements to store inline before using heap
 * 
 * This class provides API compatibility with VarLengthArray while using
 * std::vector internally for better standard library integration.
 */
template<typename T, std::size_t InlineCapacity = 256>
requires PropertyConcepts::PropertyType<T>
class SmallVector {
public:
    using value_type = T;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;
    using reference = T&;
    using const_reference = const T&;
    using pointer = T*;
    using const_pointer = const T*;
    using iterator = T*;
    using const_iterator = const T*;
    using reverse_iterator = std::reverse_iterator<iterator>;
    using const_reverse_iterator = std::reverse_iterator<const_iterator>;

private:
    // Small object optimization storage
    alignas(T) std::byte inline_storage_[sizeof(T) * InlineCapacity];
    std::vector<T> heap_storage_;
    size_type size_ = 0;
    bool using_heap_ = false;

    /**
     * @brief Get pointer to inline storage as T*
     */
    T* inline_data() noexcept {
        return reinterpret_cast<T*>(inline_storage_);
    }
    
    /**
     * @brief Get const pointer to inline storage as const T*
     */
    const T* inline_data() const noexcept {
        return reinterpret_cast<const T*>(inline_storage_);
    }
    
    /**
     * @brief Switch from inline to heap storage
     */
    void switch_to_heap() {
        if (using_heap_) return;
        
        heap_storage_.reserve(std::max(size_ * 2, InlineCapacity + 1));
        
        // Move elements from inline storage to heap
        for (size_type i = 0; i < size_; ++i) {
            heap_storage_.emplace_back(std::move(inline_data()[i]));
            inline_data()[i].~T();
        }
        
        using_heap_ = true;
    }
    
    /**
     * @brief Destroy elements in inline storage
     */
    void destroy_inline_elements() noexcept {
        if (!using_heap_) {
            for (size_type i = 0; i < size_; ++i) {
                inline_data()[i].~T();
            }
        }
    }

public:
    // ==================================================================================
    // Constructors and Destructor
    // ==================================================================================
    
    /**
     * @brief Default constructor
     */
    SmallVector() noexcept = default;
    
    /**
     * @brief Construct with initial size
     * @param count Number of elements to create
     */
    explicit SmallVector(size_type count) {
        resize(count);
    }
    
    /**
     * @brief Construct with initial size and value
     * @param count Number of elements to create
     * @param value Value to initialize elements with
     */
    SmallVector(size_type count, const T& value) {
        resize(count, value);
    }
    
    /**
     * @brief Construct from iterator range
     * @tparam InputIt Iterator type
     * @param first Beginning of range
     * @param last End of range
     */
    template<typename InputIt>
    SmallVector(InputIt first, InputIt last) {
        assign(first, last);
    }
    
    /**
     * @brief Construct from initializer list
     * @param init Initializer list
     */
    SmallVector(std::initializer_list<T> init) {
        assign(init);
    }
    
    /**
     * @brief Copy constructor
     * @param other Other SmallVector to copy from
     */
    SmallVector(const SmallVector& other) {
        assign(other.begin(), other.end());
    }
    
    /**
     * @brief Move constructor
     * @param other Other SmallVector to move from
     */
    SmallVector(SmallVector&& other) noexcept {
        if (other.using_heap_) {
            heap_storage_ = std::move(other.heap_storage_);
            using_heap_ = true;
            size_ = other.size_;
            other.size_ = 0;
            other.using_heap_ = false;
        } else {
            // Move elements from inline storage
            for (size_type i = 0; i < other.size_; ++i) {
                new (inline_data() + i) T(std::move(other.inline_data()[i]));
                other.inline_data()[i].~T();
            }
            size_ = other.size_;
            other.size_ = 0;
        }
    }
    
    /**
     * @brief Destructor
     */
    ~SmallVector() {
        destroy_inline_elements();
    }

    // ==================================================================================
    // Assignment Operators
    // ==================================================================================
    
    /**
     * @brief Copy assignment operator
     * @param other Other SmallVector to copy from
     * @return Reference to this object
     */
    SmallVector& operator=(const SmallVector& other) {
        if (this != &other) {
            clear();
            assign(other.begin(), other.end());
        }
        return *this;
    }
    
    /**
     * @brief Move assignment operator
     * @param other Other SmallVector to move from
     * @return Reference to this object
     */
    SmallVector& operator=(SmallVector&& other) noexcept {
        if (this != &other) {
            clear();
            if (other.using_heap_) {
                heap_storage_ = std::move(other.heap_storage_);
                using_heap_ = true;
                size_ = other.size_;
                other.size_ = 0;
                other.using_heap_ = false;
            } else {
                for (size_type i = 0; i < other.size_; ++i) {
                    new (inline_data() + i) T(std::move(other.inline_data()[i]));
                    other.inline_data()[i].~T();
                }
                size_ = other.size_;
                other.size_ = 0;
            }
        }
        return *this;
    }
    
    /**
     * @brief Assignment from initializer list
     * @param init Initializer list
     * @return Reference to this object
     */
    SmallVector& operator=(std::initializer_list<T> init) {
        assign(init);
        return *this;
    }

    // ==================================================================================
    // Element Access
    // ==================================================================================
    
    /**
     * @brief Access element at index (with bounds checking)
     * @param pos Index of element
     * @return Reference to element
     */
    reference at(size_type pos) {
        if (pos >= size_) {
            throw std::out_of_range("SmallVector::at: index out of range");
        }
        return (*this)[pos];
    }
    
    /**
     * @brief Access element at index (with bounds checking, const version)
     * @param pos Index of element
     * @return Const reference to element
     */
    const_reference at(size_type pos) const {
        if (pos >= size_) {
            throw std::out_of_range("SmallVector::at: index out of range");
        }
        return (*this)[pos];
    }
    
    /**
     * @brief Access element at index (no bounds checking)
     * @param pos Index of element
     * @return Reference to element
     */
    reference operator[](size_type pos) noexcept {
        return using_heap_ ? heap_storage_[pos] : inline_data()[pos];
    }
    
    /**
     * @brief Access element at index (no bounds checking, const version)
     * @param pos Index of element
     * @return Const reference to element
     */
    const_reference operator[](size_type pos) const noexcept {
        return using_heap_ ? heap_storage_[pos] : inline_data()[pos];
    }
    
    /**
     * @brief Access first element
     * @return Reference to first element
     */
    reference front() noexcept {
        return (*this)[0];
    }
    
    /**
     * @brief Access first element (const version)
     * @return Const reference to first element
     */
    const_reference front() const noexcept {
        return (*this)[0];
    }
    
    /**
     * @brief Access last element
     * @return Reference to last element
     */
    reference back() noexcept {
        return (*this)[size_ - 1];
    }
    
    /**
     * @brief Access last element (const version)
     * @return Const reference to last element
     */
    const_reference back() const noexcept {
        return (*this)[size_ - 1];
    }
    
    /**
     * @brief Get pointer to underlying data
     * @return Pointer to data
     */
    T* data() noexcept {
        return using_heap_ ? heap_storage_.data() : inline_data();
    }
    
    /**
     * @brief Get const pointer to underlying data
     * @return Const pointer to data
     */
    const T* data() const noexcept {
        return using_heap_ ? heap_storage_.data() : inline_data();
    }

    // ==================================================================================
    // Iterators
    // ==================================================================================
    
    iterator begin() noexcept { return data(); }
    const_iterator begin() const noexcept { return data(); }
    const_iterator cbegin() const noexcept { return data(); }
    
    iterator end() noexcept { return data() + size_; }
    const_iterator end() const noexcept { return data() + size_; }
    const_iterator cend() const noexcept { return data() + size_; }
    
    reverse_iterator rbegin() noexcept { return reverse_iterator(end()); }
    const_reverse_iterator rbegin() const noexcept { return const_reverse_iterator(end()); }
    const_reverse_iterator crbegin() const noexcept { return const_reverse_iterator(end()); }
    
    reverse_iterator rend() noexcept { return reverse_iterator(begin()); }
    const_reverse_iterator rend() const noexcept { return const_reverse_iterator(begin()); }
    const_reverse_iterator crend() const noexcept { return const_reverse_iterator(begin()); }

    // ==================================================================================
    // Capacity
    // ==================================================================================
    
    /**
     * @brief Check if container is empty
     * @return true if empty, false otherwise
     */
    bool empty() const noexcept {
        return size_ == 0;
    }
    
    /**
     * @brief Get number of elements
     * @return Number of elements
     */
    size_type size() const noexcept {
        return size_;
    }
    
    /**
     * @brief Get maximum possible number of elements
     * @return Maximum size
     */
    size_type max_size() const noexcept {
        return std::numeric_limits<size_type>::max();
    }
    
    /**
     * @brief Reserve capacity for at least the specified number of elements
     * @param new_cap New capacity
     */
    void reserve(size_type new_cap) {
        if (new_cap > InlineCapacity && !using_heap_) {
            switch_to_heap();
        }
        if (using_heap_) {
            heap_storage_.reserve(new_cap);
        }
    }
    
    /**
     * @brief Get current capacity
     * @return Current capacity
     */
    size_type capacity() const noexcept {
        return using_heap_ ? heap_storage_.capacity() : InlineCapacity;
    }
    
    /**
     * @brief Reduce capacity to fit current size
     */
    void shrink_to_fit() {
        if (using_heap_) {
            heap_storage_.shrink_to_fit();
            // Potentially switch back to inline storage if small enough
            if (size_ <= InlineCapacity) {
                for (size_type i = 0; i < size_; ++i) {
                    new (inline_data() + i) T(std::move(heap_storage_[i]));
                }
                heap_storage_.clear();
                using_heap_ = false;
            }
        }
    }

    // ==================================================================================
    // Modifiers
    // ==================================================================================

    /**
     * @brief Clear all elements
     */
    void clear() noexcept {
        destroy_inline_elements();
        if (using_heap_) {
            heap_storage_.clear();
        }
        size_ = 0;
    }

    /**
     * @brief Insert element at position
     * @param pos Position to insert at
     * @param value Value to insert
     * @return Iterator to inserted element
     */
    iterator insert(const_iterator pos, const T& value) {
        return emplace(pos, value);
    }

    /**
     * @brief Insert element at position (move version)
     * @param pos Position to insert at
     * @param value Value to insert
     * @return Iterator to inserted element
     */
    iterator insert(const_iterator pos, T&& value) {
        return emplace(pos, std::move(value));
    }

    /**
     * @brief Insert multiple copies of element
     * @param pos Position to insert at
     * @param count Number of copies
     * @param value Value to insert
     * @return Iterator to first inserted element
     */
    iterator insert(const_iterator pos, size_type count, const T& value) {
        auto index = pos - begin();
        for (size_type i = 0; i < count; ++i) {
            insert(begin() + index + i, value);
        }
        return begin() + index;
    }

    /**
     * @brief Insert range of elements
     * @tparam InputIt Iterator type
     * @param pos Position to insert at
     * @param first Beginning of range
     * @param last End of range
     * @return Iterator to first inserted element
     */
    template<typename InputIt>
    iterator insert(const_iterator pos, InputIt first, InputIt last) {
        auto index = pos - begin();
        auto current_index = index;
        for (auto it = first; it != last; ++it) {
            insert(begin() + current_index, *it);
            ++current_index;
        }
        return begin() + index;
    }

    /**
     * @brief Insert from initializer list
     * @param pos Position to insert at
     * @param init Initializer list
     * @return Iterator to first inserted element
     */
    iterator insert(const_iterator pos, std::initializer_list<T> init) {
        return insert(pos, init.begin(), init.end());
    }

    /**
     * @brief Emplace element at position
     * @tparam Args Constructor argument types
     * @param pos Position to emplace at
     * @param args Constructor arguments
     * @return Iterator to emplaced element
     */
    template<typename... Args>
    iterator emplace(const_iterator pos, Args&&... args) {
        auto index = pos - begin();

        if (size_ >= InlineCapacity && !using_heap_) {
            switch_to_heap();
        }

        if (using_heap_) {
            auto it = heap_storage_.emplace(heap_storage_.begin() + index, std::forward<Args>(args)...);
            ++size_;
            return &(*it);
        } else {
            // Shift elements to make room
            if (size_ < InlineCapacity) {
                for (size_type i = size_; i > index; --i) {
                    new (inline_data() + i) T(std::move(inline_data()[i - 1]));
                    inline_data()[i - 1].~T();
                }
                new (inline_data() + index) T(std::forward<Args>(args)...);
                ++size_;
                return inline_data() + index;
            } else {
                // Need to switch to heap
                switch_to_heap();
                auto it = heap_storage_.emplace(heap_storage_.begin() + index, std::forward<Args>(args)...);
                ++size_;
                return &(*it);
            }
        }
    }

    /**
     * @brief Erase element at position
     * @param pos Position to erase
     * @return Iterator to element following the erased one
     */
    iterator erase(const_iterator pos) {
        return erase(pos, pos + 1);
    }

    /**
     * @brief Erase range of elements
     * @param first Beginning of range to erase
     * @param last End of range to erase
     * @return Iterator to element following the erased range
     */
    iterator erase(const_iterator first, const_iterator last) {
        auto start_index = first - begin();
        auto end_index = last - begin();
        auto count = end_index - start_index;

        if (using_heap_) {
            auto it = heap_storage_.erase(heap_storage_.begin() + start_index,
                                        heap_storage_.begin() + end_index);
            size_ -= count;
            return &(*it);
        } else {
            // Destroy elements in range
            for (auto i = start_index; i < end_index; ++i) {
                inline_data()[i].~T();
            }

            // Shift remaining elements
            for (auto i = end_index; i < size_; ++i) {
                new (inline_data() + i - count) T(std::move(inline_data()[i]));
                inline_data()[i].~T();
            }

            size_ -= count;
            return inline_data() + start_index;
        }
    }

    /**
     * @brief Add element to end
     * @param value Value to add
     */
    void push_back(const T& value) {
        emplace_back(value);
    }

    /**
     * @brief Add element to end (move version)
     * @param value Value to add
     */
    void push_back(T&& value) {
        emplace_back(std::move(value));
    }

    /**
     * @brief Emplace element at end
     * @tparam Args Constructor argument types
     * @param args Constructor arguments
     * @return Reference to emplaced element
     */
    template<typename... Args>
    reference emplace_back(Args&&... args) {
        if (size_ >= InlineCapacity && !using_heap_) {
            switch_to_heap();
        }

        if (using_heap_) {
            auto& ref = heap_storage_.emplace_back(std::forward<Args>(args)...);
            ++size_;
            return ref;
        } else {
            new (inline_data() + size_) T(std::forward<Args>(args)...);
            ++size_;
            return inline_data()[size_ - 1];
        }
    }

    /**
     * @brief Remove last element
     */
    void pop_back() noexcept {
        if (size_ > 0) {
            --size_;
            if (using_heap_) {
                heap_storage_.pop_back();
            } else {
                inline_data()[size_].~T();
            }
        }
    }

    /**
     * @brief Resize container
     * @param count New size
     */
    void resize(size_type count) {
        resize(count, T{});
    }

    /**
     * @brief Resize container with fill value
     * @param count New size
     * @param value Value to fill new elements with
     */
    void resize(size_type count, const T& value) {
        if (count > size_) {
            // Grow
            while (size_ < count) {
                push_back(value);
            }
        } else if (count < size_) {
            // Shrink
            while (size_ > count) {
                pop_back();
            }
        }
    }

    /**
     * @brief Assign from iterator range
     * @tparam InputIt Iterator type
     * @param first Beginning of range
     * @param last End of range
     */
    template<typename InputIt>
    void assign(InputIt first, InputIt last) {
        clear();
        for (auto it = first; it != last; ++it) {
            push_back(*it);
        }
    }

    /**
     * @brief Assign from initializer list
     * @param init Initializer list
     */
    void assign(std::initializer_list<T> init) {
        assign(init.begin(), init.end());
    }

    /**
     * @brief Assign count copies of value
     * @param count Number of copies
     * @param value Value to copy
     */
    void assign(size_type count, const T& value) {
        clear();
        resize(count, value);
    }

    /**
     * @brief Swap with another SmallVector
     * @param other Other vector to swap with
     */
    void swap(SmallVector& other) noexcept {
        // This is a simplified implementation
        // A more efficient version would handle the inline/heap cases separately
        SmallVector temp = std::move(*this);
        *this = std::move(other);
        other = std::move(temp);
    }

    // ==================================================================================
    // Comparison Operators
    // ==================================================================================

    friend bool operator==(const SmallVector& lhs, const SmallVector& rhs) noexcept {
        return lhs.size_ == rhs.size_ && std::equal(lhs.begin(), lhs.end(), rhs.begin());
    }

    friend bool operator!=(const SmallVector& lhs, const SmallVector& rhs) noexcept {
        return !(lhs == rhs);
    }

    friend bool operator<(const SmallVector& lhs, const SmallVector& rhs) noexcept {
        return std::lexicographical_compare(lhs.begin(), lhs.end(), rhs.begin(), rhs.end());
    }

    friend bool operator<=(const SmallVector& lhs, const SmallVector& rhs) noexcept {
        return !(rhs < lhs);
    }

    friend bool operator>(const SmallVector& lhs, const SmallVector& rhs) noexcept {
        return rhs < lhs;
    }

    friend bool operator>=(const SmallVector& lhs, const SmallVector& rhs) noexcept {
        return !(lhs < rhs);
    }
};

// ==================================================================================
// Utility Functions
// ==================================================================================

/**
 * @brief Swap two SmallVector objects
 * @tparam T Element type
 * @tparam InlineCapacity Inline capacity
 * @param lhs First vector
 * @param rhs Second vector
 */
template<typename T, std::size_t InlineCapacity>
void swap(SmallVector<T, InlineCapacity>& lhs, SmallVector<T, InlineCapacity>& rhs) noexcept {
    lhs.swap(rhs);
}

} // namespace PropertyContainers

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertyContainers::SmallVector;
using PropertyContainers::SmallVectorBase;

#endif // MODERN_CONTAINER_H
