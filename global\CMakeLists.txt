set(MODULE global)
set(MODULE_SRC
    global.h
    utils/uuid.h
    utils/uuid.cpp
    utils/threadpool.h
    utils/utils.h

    types/id.h
    types/metatype.h
    types/typecast.h
    types/typetraits.h
    types/typetraits_qt.h
    types/variant.h
    types/variant_marco.h
    types/concepts.h
    types/modern_shared_ptr.h
    types/modern_container.h
    types/modern_binding_vtable.h
    types/modern_property_binding.h
    types/types.h

    platform.h
    logger.h
    logging.h

    modularity/context.h
    modularity/imoduleinterface.h
    modularity/imodulesetup.h
    modularity/injectable.h
    modularity/ioc.h
    modularity/moduleinfo.h
    modularity/modulesioc.h
)

add_library(${MODULE})

target_sources(${MODULE} PRIVATE ${MODULE_SRC})

target_include_directories(${MODULE} PUBLIC
    ${PROJECT_BINARY_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/types
)

if (USE_SPDLOG)
    set(MODULE_LINK spdlog::spdlog)
endif(USE_SPDLOG)

add_subdirectory(io)

set(MODULE_LINK io ${MODULE_LINK})

target_link_libraries(${MODULE} PUBLIC ${MODULE_LINK})
