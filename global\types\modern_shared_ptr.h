#ifndef MODERN_SHARED_PTR_H
#define MODERN_SHARED_PTR_H

#include <memory>
#include <atomic>
#include <type_traits>
#include "concepts.h"

/**
 * @file modern_shared_ptr.h
 * @brief Modern C++20 shared pointer implementation using std::shared_ptr
 * 
 * This file provides a modern replacement for the custom SharedDataPointer
 * implementation, using std::shared_ptr as the underlying mechanism while
 * maintaining API compatibility.
 */

namespace PropertyPointers {

// ==================================================================================
// Base Classes for Shared Data
// ==================================================================================

/**
 * @brief Modern replacement for SharedData base class
 * 
 * This class provides a simple base for objects that need to be managed
 * by shared pointers. Unlike the original SharedData, this doesn't need
 * custom reference counting as std::shared_ptr handles that.
 */
class SharedDataBase {
public:
    SharedDataBase() = default;
    SharedDataBase(const SharedDataBase&) = default;
    SharedDataBase& operator=(const SharedDataBase&) = default;
    SharedDataBase(SharedDataBase&&) = default;
    SharedDataBase& operator=(SharedDataBase&&) = default;
    virtual ~SharedDataBase() = default;
    
    /**
     * @brief Get the current reference count
     * @return Reference count (for debugging/compatibility)
     * @note This is provided for compatibility but may not be accurate
     *       in multi-threaded scenarios due to std::shared_ptr limitations
     */
    long use_count() const noexcept {
        // This is a placeholder - actual use_count comes from shared_ptr
        return 1;
    }
};

// ==================================================================================
// Modern Shared Pointer Wrapper
// ==================================================================================

/**
 * @brief Modern shared pointer wrapper using std::shared_ptr
 * @tparam T Type to be managed, must be derived from SharedDataBase
 * 
 * This class provides API compatibility with the original SharedDataPointer
 * while using std::shared_ptr internally for better standard library integration.
 */
template <typename T>
requires std::derived_from<T, SharedDataBase>
class ModernSharedPtr {
public:
    using element_type = T;
    using pointer = T*;
    using weak_type = std::weak_ptr<T>;

private:
    std::shared_ptr<T> ptr_;

public:
    // ==================================================================================
    // Constructors and Destructor
    // ==================================================================================
    
    /**
     * @brief Default constructor - creates empty pointer
     */
    constexpr ModernSharedPtr() noexcept = default;
    
    /**
     * @brief Construct from raw pointer
     * @param p Raw pointer to take ownership of
     */
    explicit ModernSharedPtr(T* p) : ptr_(p) {}
    
    /**
     * @brief Copy constructor
     * @param other Other ModernSharedPtr to copy from
     */
    ModernSharedPtr(const ModernSharedPtr& other) noexcept = default;
    
    /**
     * @brief Move constructor
     * @param other Other ModernSharedPtr to move from
     */
    ModernSharedPtr(ModernSharedPtr&& other) noexcept = default;
    
    /**
     * @brief Construct from std::shared_ptr
     * @param p std::shared_ptr to wrap
     */
    explicit ModernSharedPtr(std::shared_ptr<T> p) noexcept : ptr_(std::move(p)) {}
    
    /**
     * @brief Destructor
     */
    ~ModernSharedPtr() = default;

    // ==================================================================================
    // Assignment Operators
    // ==================================================================================
    
    /**
     * @brief Copy assignment operator
     * @param other Other ModernSharedPtr to copy from
     * @return Reference to this object
     */
    ModernSharedPtr& operator=(const ModernSharedPtr& other) noexcept = default;
    
    /**
     * @brief Move assignment operator
     * @param other Other ModernSharedPtr to move from
     * @return Reference to this object
     */
    ModernSharedPtr& operator=(ModernSharedPtr&& other) noexcept = default;
    
    /**
     * @brief Assignment from raw pointer
     * @param p Raw pointer to take ownership of
     * @return Reference to this object
     */
    ModernSharedPtr& operator=(T* p) {
        ptr_.reset(p);
        return *this;
    }

    // ==================================================================================
    // Access Operators
    // ==================================================================================
    
    /**
     * @brief Dereference operator
     * @return Reference to the managed object
     */
    T& operator*() const noexcept {
        return *ptr_;
    }
    
    /**
     * @brief Arrow operator
     * @return Pointer to the managed object
     */
    T* operator->() const noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Boolean conversion operator
     * @return true if pointer is not null, false otherwise
     */
    explicit operator bool() const noexcept {
        return static_cast<bool>(ptr_);
    }
    
    /**
     * @brief Negation operator
     * @return true if pointer is null, false otherwise
     */
    bool operator!() const noexcept {
        return !ptr_;
    }

    // ==================================================================================
    // Compatibility Methods (SharedDataPointer API)
    // ==================================================================================
    
    /**
     * @brief Get raw pointer (const version)
     * @return Raw pointer to managed object
     */
    const T* get() const noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Get raw pointer (non-const version)
     * @return Raw pointer to managed object
     * @note For compatibility with SharedDataPointer API
     */
    T* get() noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Get raw pointer (const version)
     * @return Raw pointer to managed object
     */
    const T* data() const noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Get raw pointer (non-const version)
     * @return Raw pointer to managed object
     */
    T* data() noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Get const raw pointer
     * @return Const raw pointer to managed object
     */
    const T* constData() const noexcept {
        return ptr_.get();
    }
    
    /**
     * @brief Release ownership and return raw pointer
     * @return Raw pointer to managed object (caller takes ownership)
     * @warning This breaks shared ownership semantics - use with caution
     * @note This method is provided for compatibility but should be avoided
     *       as it breaks the shared ownership model
     */
    T* take() noexcept {
        // std::shared_ptr doesn't have release(), so we simulate it
        // This only works safely if use_count() == 1
        if (ptr_ && ptr_.use_count() == 1) {
            T* result = ptr_.get();
            ptr_.reset();
            return result;
        }
        // If there are multiple references, we can't safely release
        // Return nullptr to indicate failure
        return nullptr;
    }
    
    /**
     * @brief Reset the pointer
     * @param p New pointer to manage (default: nullptr)
     */
    void reset(T* p = nullptr) noexcept {
        ptr_.reset(p);
    }
    
    /**
     * @brief Get reference count
     * @return Current reference count
     */
    long use_count() const noexcept {
        return ptr_.use_count();
    }
    
    /**
     * @brief Check if this is the only reference
     * @return true if use_count() == 1, false otherwise
     */
    bool unique() const noexcept {
        return ptr_.unique();
    }
    
    /**
     * @brief Swap with another ModernSharedPtr
     * @param other Other pointer to swap with
     */
    void swap(ModernSharedPtr& other) noexcept {
        ptr_.swap(other.ptr_);
    }

    // ==================================================================================
    // Standard Library Integration
    // ==================================================================================
    
    /**
     * @brief Get the underlying std::shared_ptr
     * @return Reference to the underlying std::shared_ptr
     */
    const std::shared_ptr<T>& shared_ptr() const noexcept {
        return ptr_;
    }
    
    /**
     * @brief Get the underlying std::shared_ptr (non-const)
     * @return Reference to the underlying std::shared_ptr
     */
    std::shared_ptr<T>& shared_ptr() noexcept {
        return ptr_;
    }
    
    /**
     * @brief Create a weak_ptr from this shared_ptr
     * @return weak_ptr pointing to the same object
     */
    std::weak_ptr<T> weak_ptr() const noexcept {
        return std::weak_ptr<T>(ptr_);
    }

    // ==================================================================================
    // Comparison Operators
    // ==================================================================================
    
    friend bool operator==(const ModernSharedPtr& lhs, const ModernSharedPtr& rhs) noexcept {
        return lhs.ptr_ == rhs.ptr_;
    }
    
    friend bool operator!=(const ModernSharedPtr& lhs, const ModernSharedPtr& rhs) noexcept {
        return !(lhs == rhs);
    }
    
    friend bool operator<(const ModernSharedPtr& lhs, const ModernSharedPtr& rhs) noexcept {
        return lhs.ptr_ < rhs.ptr_;
    }
    
    friend bool operator<=(const ModernSharedPtr& lhs, const ModernSharedPtr& rhs) noexcept {
        return !(rhs < lhs);
    }
    
    friend bool operator>(const ModernSharedPtr& lhs, const ModernSharedPtr& rhs) noexcept {
        return rhs < lhs;
    }
    
    friend bool operator>=(const ModernSharedPtr& lhs, const ModernSharedPtr& rhs) noexcept {
        return !(lhs < rhs);
    }
    
    // Comparison with nullptr
    friend bool operator==(const ModernSharedPtr& lhs, std::nullptr_t) noexcept {
        return !lhs.ptr_;
    }
    
    friend bool operator==(std::nullptr_t, const ModernSharedPtr& rhs) noexcept {
        return !rhs.ptr_;
    }
    
    friend bool operator!=(const ModernSharedPtr& lhs, std::nullptr_t) noexcept {
        return static_cast<bool>(lhs.ptr_);
    }
    
    friend bool operator!=(std::nullptr_t, const ModernSharedPtr& rhs) noexcept {
        return static_cast<bool>(rhs.ptr_);
    }
};

// ==================================================================================
// Utility Functions
// ==================================================================================

/**
 * @brief Create a ModernSharedPtr with make_shared
 * @tparam T Type to create
 * @tparam Args Constructor argument types
 * @param args Constructor arguments
 * @return ModernSharedPtr managing the new object
 */
template<typename T, typename... Args>
requires std::derived_from<T, SharedDataBase>
ModernSharedPtr<T> make_modern_shared(Args&&... args) {
    return ModernSharedPtr<T>(std::make_shared<T>(std::forward<Args>(args)...));
}

/**
 * @brief Swap two ModernSharedPtr objects
 * @tparam T Element type
 * @param lhs First pointer
 * @param rhs Second pointer
 */
template<typename T>
void swap(ModernSharedPtr<T>& lhs, ModernSharedPtr<T>& rhs) noexcept {
    lhs.swap(rhs);
}

} // namespace PropertyPointers

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

using PropertyPointers::ModernSharedPtr;
using PropertyPointers::SharedDataBase;
using PropertyPointers::makeShared;

#endif // MODERN_SHARED_PTR_H
