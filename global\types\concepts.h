#ifndef CONCEPTS_H
#define CONCEPTS_H

#include <concepts>
#include <type_traits>
#include <utility>
#include <iterator>
#include <limits>

/**
 * @file concepts.h
 * @brief Modern C++20 concepts to replace custom type traits
 * 
 * This file provides C++20 concepts that replace the custom TypeTraits
 * implementation, offering better compile-time error messages and
 * standardized type checking.
 */

namespace PropertyConcepts {

// ==================================================================================
// Basic Type Concepts
// ==================================================================================

/**
 * @brief Concept to check if a type has equality comparison operator
 * @tparam T Type to check
 */
template<typename T>
concept EqualityComparable = requires(const T& a, const T& b) {
    { a == b } -> std::convertible_to<bool>;
};

/**
 * @brief Concept to check if a type has less-than comparison operator
 * @tparam T Type to check
 */
template<typename T>
concept LessThanComparable = requires(const T& a, const T& b) {
    { a < b } -> std::convertible_to<bool>;
};

/**
 * @brief Concept to check if a type is dereferenceable (has operator->)
 * @tparam T Type to check
 */
template<typename T>
concept Dereferenceable = requires(T& t) {
    { t.operator->() };
};

/**
 * @brief Concept to check if a type is a container
 * @tparam T Type to check
 */
template<typename T>
concept Container = requires(T& t) {
    typename T::value_type;
    t.begin();
    t.end();
    { t.begin() != t.end() } -> std::convertible_to<bool>;
};

// ==================================================================================
// Property-Specific Concepts
// ==================================================================================

/**
 * @brief Concept for types that can be used as property values
 * @tparam T Type to check
 * 
 * Property types must be copy constructible and move constructible.
 * They should also be destructible and assignable.
 */
template<typename T>
concept PropertyType = std::copy_constructible<T> && 
                      std::move_constructible<T> && 
                      std::destructible<T> &&
                      std::assignable_from<T&, const T&> &&
                      std::assignable_from<T&, T&&>;

/**
 * @brief Concept for types that can be compared for equality in property context
 * @tparam T Type to check
 */
template<typename T>
concept PropertyEqualityComparable = PropertyType<T> && EqualityComparable<T>;

/**
 * @brief Concept for types that support arrow operator in property context
 * @tparam T Type to check
 */
template<typename T>
concept PropertyDereferenceable = PropertyType<T> && 
                                 (std::is_pointer_v<T> || Dereferenceable<T>);

/**
 * @brief Concept for callable objects that can be used as property bindings
 * @tparam F Functor type
 * @tparam R Expected return type
 */
template<typename F, typename R>
concept PropertyBindingCallable = std::invocable<F> &&
                                 std::convertible_to<std::invoke_result_t<F>, R>;

/**
 * @brief Concept for observer callback functions
 * @tparam F Functor type
 */
template<typename F>
concept PropertyObserverCallable = std::invocable<F> && 
                                  std::same_as<std::invoke_result_t<F>, void>;

// ==================================================================================
// Memory Management Concepts
// ==================================================================================

/**
 * @brief Concept for types that can be used with reference counting
 * @tparam T Type to check
 */
template<typename T>
concept RefCountable = requires(T& t) {
    { t.addRef() } -> std::same_as<void>;
    { t.deref() } -> std::convertible_to<bool>;
    { t.refCount() } -> std::convertible_to<int>;
};

/**
 * @brief Concept for smart pointer types
 * @tparam T Type to check
 */
template<typename T>
concept SmartPointer = requires(T& t) {
    typename T::element_type;
    { t.get() } -> std::convertible_to<typename T::element_type*>;
    { t.operator->() } -> std::convertible_to<typename T::element_type*>;
    { t.operator*() } -> std::convertible_to<typename T::element_type&>;
    { static_cast<bool>(t) } -> std::same_as<bool>;
};

// ==================================================================================
// Utility Concepts
// ==================================================================================

/**
 * @brief Concept for arithmetic types that can be promoted
 * @tparam T First type
 * @tparam U Second type
 */
template<typename T, typename U>
concept ArithmeticPromotable = std::is_arithmetic_v<T> && 
                              std::is_arithmetic_v<U> &&
                              std::is_floating_point_v<T> == std::is_floating_point_v<U> &&
                              std::is_signed_v<T> == std::is_signed_v<U> &&
                              !std::is_same_v<T, bool> && 
                              !std::is_same_v<U, bool> &&
                              !std::is_same_v<T, char> && 
                              !std::is_same_v<U, char>;

/**
 * @brief Concept for types that use reference semantics in properties
 * @tparam T Type to check
 */
template<typename T>
concept UseReferenceSemantics = !(std::is_arithmetic_v<T> || 
                                 std::is_enum_v<T> || 
                                 std::is_pointer_v<T>);

// ==================================================================================
// Type Aliases for Backward Compatibility
// ==================================================================================

/**
 * @brief Helper to determine parameter type based on reference semantics
 * @tparam T Value type
 */
template<typename T>
using parameter_type_t = std::conditional_t<UseReferenceSemantics<T>, const T&, T>;

/**
 * @brief Helper to determine rvalue reference type
 * @tparam T Value type
 */
template<typename T>
using rvalue_ref_t = std::conditional_t<UseReferenceSemantics<T>, T&&, T>;

/**
 * @brief Helper to determine arrow operator result type
 * @tparam T Value type
 */
template<typename T>
using arrow_operator_result_t = std::conditional_t<std::is_pointer_v<T>, const T&,
                                std::conditional_t<Dereferenceable<T>, const T&, void>>;

} // namespace PropertyConcepts

// ==================================================================================
// Backward Compatibility Aliases
// ==================================================================================

// 导出主要概念到全局命名空间
using PropertyConcepts::PropertyType;
using PropertyConcepts::EqualityComparable;
using PropertyConcepts::LessThanComparable;
using PropertyConcepts::Dereferenceable;
using PropertyConcepts::ArithmeticPromotable;

#endif // CONCEPTS_H
