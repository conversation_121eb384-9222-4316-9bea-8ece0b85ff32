/**
 * @file Core/PropertyData.h
 * @brief 基础属性数据类实现
 * 
 * 这个文件实现了Property系统的核心数据存储类，
 * 提供类型安全的属性值存储和基础操作。
 */

#ifndef PROPERTY_SYSTEM_CORE_PROPERTY_DATA_H
#define PROPERTY_SYSTEM_CORE_PROPERTY_DATA_H

#include "PropertyTypes.h"
#include <utility>
#include <type_traits>
#include <cstdio>

namespace PropertySystem::Core {

// ==================================================================================
// 未类型化属性数据基类
// ==================================================================================

/**
 * @brief 未类型化的属性数据基类
 * 
 * 这个类为所有属性数据提供统一的基类接口，
 * 支持类型擦除和多态操作。
 */
class UntypedPropertyData {
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~UntypedPropertyData() = default;

    /**
     * @brief 检查属性是否有绑定
     * @return 如果属性有绑定则返回true
     */
    virtual bool hasBinding() const = 0;

    /**
     * @brief 移除绑定（除非在包装器中）
     */
    virtual void removeBindingUnlessInWrapper() = 0;

    /**
     * @brief 获取属性的类型信息（用于调试）
     * @return 类型名称字符串
     */
    virtual const char* typeName() const = 0;

protected:
    UntypedPropertyData() = default;
    UntypedPropertyData(const UntypedPropertyData&) = default;
    UntypedPropertyData(UntypedPropertyData&&) = default;
    UntypedPropertyData& operator=(const UntypedPropertyData&) = default;
    UntypedPropertyData& operator=(UntypedPropertyData&&) = default;
};

// ==================================================================================
// 类型化属性数据模板类
// ==================================================================================

/**
 * @brief 类型化的属性数据模板类
 * @tparam T 属性值类型
 * 
 * 这个类提供类型安全的属性值存储，支持绑定系统和观察者模式。
 */
template<typename T>
requires PropertyType<T>
class PropertyData : public UntypedPropertyData {
public:
    // ==================================================================================
    // 类型定义
    // ==================================================================================
    
    using value_type = T;
    using parameter_type = ParameterType<T>;
    using rvalue_ref = RValueRef<T>;

protected:
    // ==================================================================================
    // 成员变量
    // ==================================================================================
    
    mutable T val = T();  ///< 属性值存储

public:
    // ==================================================================================
    // 构造函数
    // ==================================================================================
    
    /**
     * @brief 默认构造函数
     */
    PropertyData() = default;

    /**
     * @brief 从值构造
     * @param t 初始值
     */
    PropertyData(parameter_type t) : val(t) {}

    /**
     * @brief 从右值构造
     * @param t 初始值（右值引用）
     */
    PropertyData(rvalue_ref t) requires UseReferenceSemantics<T>
        : val(std::move(t)) {}

    /**
     * @brief 拷贝构造函数
     */
    PropertyData(const PropertyData& other) : val(other.val) {}

    /**
     * @brief 移动构造函数
     */
    PropertyData(PropertyData&& other) noexcept : val(std::move(other.val)) {}

    // ==================================================================================
    // 赋值运算符
    // ==================================================================================
    
    /**
     * @brief 拷贝赋值运算符
     */
    PropertyData& operator=(const PropertyData& other) {
        if (this != &other) {
            val = other.val;
        }
        return *this;
    }

    /**
     * @brief 移动赋值运算符
     */
    PropertyData& operator=(PropertyData&& other) noexcept {
        if (this != &other) {
            val = std::move(other.val);
        }
        return *this;
    }

    /**
     * @brief 值赋值运算符
     */
    PropertyData& operator=(parameter_type t) {
        val = t;
        return *this;
    }

    /**
     * @brief 右值赋值运算符
     */
    PropertyData& operator=(rvalue_ref t) requires UseReferenceSemantics<T> {
        val = std::move(t);
        return *this;
    }

    // ==================================================================================
    // 值访问方法
    // ==================================================================================
    
    /**
     * @brief 获取属性值（绕过绑定）
     * @return 属性值的常量引用
     */
    parameter_type valueBypassingBindings() const { 
        return val; 
    }

    /**
     * @brief 设置属性值（绕过绑定）
     * @param t 新的属性值
     */
    void setValueBypassingBindings(parameter_type t) { 
        val = t; 
    }

    /**
     * @brief 设置属性值（绕过绑定，右值版本）
     * @param t 新的属性值（右值引用）
     */
    void setValueBypassingBindings(rvalue_ref t) requires UseReferenceSemantics<T> { 
        val = std::move(t); 
    }

    /**
     * @brief 获取属性值的可变引用（内部使用）
     * @return 属性值的可变引用
     */
    T& valueRef() { 
        return val; 
    }

    /**
     * @brief 获取属性值的常量引用（内部使用）
     * @return 属性值的常量引用
     */
    const T& valueRef() const { 
        return val; 
    }

    // ==================================================================================
    // 类型转换运算符
    // ==================================================================================
    
    /**
     * @brief 隐式转换为值类型
     * @return 属性值
     */
    operator parameter_type() const { 
        return valueBypassingBindings(); 
    }

    // ==================================================================================
    // 比较运算符
    // ==================================================================================
    
    /**
     * @brief 相等比较运算符
     * @param other 另一个PropertyData对象
     * @return 如果值相等则返回true
     */
    bool operator==(const PropertyData& other) const requires EqualityComparable<T> {
        return val == other.val;
    }

    /**
     * @brief 不等比较运算符
     * @param other 另一个PropertyData对象
     * @return 如果值不等则返回true
     */
    bool operator!=(const PropertyData& other) const requires EqualityComparable<T> {
        return !(*this == other);
    }

    /**
     * @brief 与值的相等比较
     * @param value 要比较的值
     * @return 如果属性值等于给定值则返回true
     */
    bool operator==(parameter_type value) const requires EqualityComparable<T> {
        return val == value;
    }

    /**
     * @brief 与值的不等比较
     * @param value 要比较的值
     * @return 如果属性值不等于给定值则返回true
     */
    bool operator!=(parameter_type value) const requires EqualityComparable<T> {
        return val != value;
    }

    // ==================================================================================
    // 虚函数实现
    // ==================================================================================
    
    /**
     * @brief 检查属性是否有绑定
     * @return 如果属性有绑定则返回true
     */
    bool hasBinding() const override {
        // 默认实现：无绑定
        // 在Property类中会被重写
        return false;
    }

    /**
     * @brief 移除绑定（除非在包装器中）
     */
    void removeBindingUnlessInWrapper() override {
        // 默认实现：无操作
        // 在Property类中会被重写
    }

    /**
     * @brief 获取属性的类型信息
     * @return 类型名称字符串
     */
    const char* typeName() const override {
        return typeid(T).name();
    }

    // ==================================================================================
    // 工具方法
    // ==================================================================================
    
    /**
     * @brief 交换两个PropertyData对象的值
     * @param other 另一个PropertyData对象
     */
    void swap(PropertyData& other) noexcept {
        using std::swap;
        swap(val, other.val);
    }

    /**
     * @brief 重置属性值为默认值
     */
    void reset() {
        val = T{};
    }

    /**
     * @brief 检查属性值是否为默认值
     * @return 如果是默认值则返回true
     */
    bool isDefault() const requires EqualityComparable<T> {
        return val == T{};
    }
};

// ==================================================================================
// 非成员函数
// ==================================================================================

/**
 * @brief 交换两个PropertyData对象
 * @tparam T 属性值类型
 * @param lhs 第一个PropertyData对象
 * @param rhs 第二个PropertyData对象
 */
template<typename T>
void swap(PropertyData<T>& lhs, PropertyData<T>& rhs) noexcept {
    lhs.swap(rhs);
}

/**
 * @brief 值与PropertyData的相等比较（左操作数为值）
 * @tparam T 属性值类型
 * @param value 要比较的值
 * @param property PropertyData对象
 * @return 如果值相等则返回true
 */
template<typename T>
bool operator==(const T& value, const PropertyData<T>& property) requires EqualityComparable<T> {
    return property == value;
}

/**
 * @brief 值与PropertyData的不等比较（左操作数为值）
 * @tparam T 属性值类型
 * @param value 要比较的值
 * @param property PropertyData对象
 * @return 如果值不等则返回true
 */
template<typename T>
bool operator!=(const T& value, const PropertyData<T>& property) requires EqualityComparable<T> {
    return property != value;
}

} // namespace PropertySystem::Core

// ==================================================================================
// 全局命名空间别名（向后兼容）
// ==================================================================================

using PropertySystem::Core::UntypedPropertyData;
using PropertySystem::Core::PropertyData;

#endif // PROPERTY_SYSTEM_CORE_PROPERTY_DATA_H
