﻿// Copyright (C) 2020 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

#ifndef TaggedPointer_H
#define TaggedPointer_H

#include <cassert>
#include <cstdint>
#include <type_traits>
#include "types.h"

constexpr std::uint8_t nextByteSize(std::uint8_t bits) { return std::uint8_t((bits + 7) / 8); }

constexpr int constexprCountTrailingZeroBits(unsigned int value) {
    if (value == 0) {
        return sizeof(value) * 8; // 所有位都是零
    }
    int count = 0;
    while ((value & 1) == 0) {
        value >>= 1; // 右移一位
        ++count;     // 增加计数
    }
    return count;
}

constexpr unsigned int constexprNextPowerOfTwo(unsigned int value) {
    if (value == 0) return 1; // 0 的下一个 2 的幂是 1
    if (value == 1) return 1; // 1 的下一个 2 的幂是 1

    // 如果 value 是 2 的幂，直接返回
    if ((value & (value - 1)) == 0) {
        return value;
    }

    unsigned int power = 1;
    while (power < value) {
        power <<= 1; // 左移一位，相当于乘以 2
    }
    return power;
}

template <typename T>
struct TagInfo
{
    static constexpr size_t alignment = alignof(T);
    static_assert((alignment & (alignment - 1)) == 0,
                  "Alignment of template parameter must be power of two");

    static constexpr std::uint8_t tagBits = std::uint8_t{constexprCountTrailingZeroBits(alignment)};
    static_assert(tagBits > 0,
                  "Alignment of template parameter does not allow any tags");

    static constexpr size_t tagSize = constexprNextPowerOfTwo(nextByteSize(tagBits));
    static_assert(tagSize < sizeof(std::uintptr_t),
                  "Alignment of template parameter allows tags masking away pointer");

    using TagType = typename IntegerForSize<tagSize>::Unsigned;
};


template <typename T, typename Tag = typename TagInfo<T>::TagType>
class TaggedPointer
{
public:
    using Type = T;
    using TagType = Tag;

    static constexpr std::uintptr_t tagMask() { return TagInfo<T>::alignment - 1; }
    static constexpr std::uintptr_t pointerMask() { return ~tagMask(); }

    [[nodiscard]]
    constexpr TaggedPointer() noexcept : d(0) {}
    [[nodiscard]]
    constexpr TaggedPointer(std::nullptr_t) noexcept : TaggedPointer() {}

    [[nodiscard]]
    explicit TaggedPointer(T *pointer, Tag tag = Tag()) noexcept
        : d(std::uintptr_t(pointer) | std::uintptr_t(tag))
    {
        static_assert(sizeof(Type*) == sizeof(TaggedPointer));

        // Q_ASSERT_X((std::uintptr_t(pointer) & tagMask()) == 0, "TaggedPointer<T, Tag>", "Pointer is not aligned");
        // Q_ASSERT_X((static_cast<typename TagInfo<T>::TagType>(tag) & pointerMask()) == 0,
        //            "TaggedPointer<T, Tag>::setTag", "Tag is larger than allowed by number of available tag bits");
        assert((std::uintptr_t(pointer) & tagMask()) == 0);
        assert((static_cast<typename TagInfo<T>::TagType>(tag) & pointerMask()) == 0);
    }

    Type &operator*() const noexcept
    {
        assert(data());
        return *data();
    }

    Type *operator->() const noexcept
    {
        return data();
    }

    explicit operator bool() const noexcept
    {
        return !isNull();
    }

    // Disables the usage of `ptr = {}`, which would go through this operator
    // (rather than using the implicitly-generated assignment operator).
    // The operators have different semantics: the ones here leave the tag intact,
    // the implicitly-generated one overwrites it.
    template <typename U,
             std::enable_if_t<std::is_convertible_v<U *, T *>, bool> = false>
    TaggedPointer &operator=(U *other) noexcept
    {
        T *otherT = other;
        d = reinterpret_cast<std::uintptr_t>(otherT) | (d & tagMask());
        return *this;
    }

    template <typename U,
             std::enable_if_t<std::is_null_pointer_v<U>, bool> = false>
    TaggedPointer &operator=(U) noexcept
    {
        d = reinterpret_cast<std::uintptr_t>(static_cast<T *>(nullptr)) | (d & tagMask());
        return *this;
    }

    static constexpr Tag maximumTag() noexcept
    {
        return TagType(typename TagInfo<T>::TagType(tagMask()));
    }

    void setTag(Tag tag)
    {
        // "TaggedPointer<T, Tag>::setTag", "Tag is larger than allowed by number of available tag bits"
        assert((static_cast<std::uintptr_t>(tag) & pointerMask()) == 0);

        d = (d & pointerMask()) | static_cast<std::uintptr_t>(tag);
    }

    Tag tag() const noexcept
    {
        return TagType(typename TagInfo<T>::TagType(d & tagMask()));
    }

    T* data() const noexcept
    {
        return reinterpret_cast<T*>(d & pointerMask());
    }

    bool isNull() const noexcept
    {
        return !data();
    }

    void swap(TaggedPointer &other) noexcept
    {
        std::swap(d, other.d);
    }

    friend inline bool operator==(TaggedPointer lhs, TaggedPointer rhs) noexcept
    {
        return lhs.data() == rhs.data();
    }

    friend inline bool operator!=(TaggedPointer lhs, TaggedPointer rhs) noexcept
    {
        return lhs.data() != rhs.data();
    }

    friend inline bool operator==(TaggedPointer lhs, std::nullptr_t) noexcept
    {
        return lhs.isNull();
    }

    friend inline bool operator==(std::nullptr_t, TaggedPointer rhs) noexcept
    {
        return rhs.isNull();
    }

    friend inline bool operator!=(TaggedPointer lhs, std::nullptr_t) noexcept
    {
        return !lhs.isNull();
    }

    friend inline bool operator!=(std::nullptr_t, TaggedPointer rhs) noexcept
    {
        return !rhs.isNull();
    }

    friend inline bool operator!(TaggedPointer ptr) noexcept
    {
        return !ptr.data();
    }

    friend inline void swap(TaggedPointer &p1, TaggedPointer &p2) noexcept
    {
        p1.swap(p2);
    }

protected:
    std::uintptr_t d;
};

template <typename T, typename Tag>
constexpr inline std::size_t qHash(TaggedPointer<T, Tag> p, std::size_t seed = 0) noexcept
{ return qHash(p.data(), seed); }

// template <typename T, typename Tag>
// class TypeInfo<TaggedPointer<T, Tag>>
//     : public TypeInfoMerger<TaggedPointer<T, Tag>, std::uintptr_t> {};

#endif // TaggedPointer_H
