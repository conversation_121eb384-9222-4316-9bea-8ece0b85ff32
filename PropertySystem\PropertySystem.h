/**
 * @file PropertySystem.h
 * @brief Property系统统一包含头文件
 * 
 * 这个头文件提供了Property系统的完整接口，
 * 包含所有必要的组件并保持向后兼容性。
 */

#ifndef PROPERTY_SYSTEM_H
#define PROPERTY_SYSTEM_H

// ==================================================================================
// 核心组件
// ==================================================================================

#include "Core/PropertyTypes.h"
#include "Core/PropertyData.h"

// ==================================================================================
// 绑定系统
// ==================================================================================

#include "Binding/BindingLocation.h"

// ==================================================================================
// 观察者系统
// ==================================================================================

#include "Observer/PropertyObserver.h"

// ==================================================================================
// 工具类
// ==================================================================================

#include "Utility/UpdateGroup.h"

// ==================================================================================
// 现代化组件
// ==================================================================================

#include "../global/types/concepts.h"
#include "../global/types/modern_shared_ptr.h"
#include "../global/types/modern_container.h"
#include "../global/types/modern_binding_vtable.h"

/**
 * @brief Property系统的主命名空间
 * 
 * 这个命名空间包含了Property系统的所有组件，
 * 提供了模块化的组织结构。
 */
namespace PropertySystem {

// ==================================================================================
// 子命名空间导入
// ==================================================================================

// 核心组件
using namespace Core;

// 绑定组件
using namespace Binding;

// 观察者组件
using namespace Observer;

// 工具组件
using namespace Utility;

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建属性绑定的便利函数
 * @tparam Functor 可调用对象类型
 * @param f 绑定函数
 * @param location 源代码位置
 * @return 属性绑定对象
 */
template<typename Functor>
requires std::invocable<Functor> && PropertyType<std::invoke_result_t<Functor>>
auto makePropertyBinding(Functor &&f, 
                        const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    using ReturnType = std::invoke_result_t<Functor>;
    return PropertyBinding<ReturnType>(std::forward<Functor>(f), location);
}

/**
 * @brief 创建属性观察者的便利函数
 * @tparam PropertyType 属性类型
 * @tparam Handler 处理器类型
 * @param property 要观察的属性
 * @param handler 变化处理函数
 * @return 属性观察者对象
 */
template<typename PropertyType, typename Handler>
auto makePropertyObserver(PropertyType *property, Handler &&handler) {
    return PropertyChangeHandler<std::remove_reference_t<Handler>>(
        property, std::forward<Handler>(handler));
}

/**
 * @brief 创建属性数据的便利函数
 * @tparam T 属性值类型
 * @param value 初始值
 * @return PropertyData对象
 */
template<typename T>
requires PropertyType<T>
auto makePropertyData(T&& value) {
    return PropertyData<std::remove_reference_t<T>>(std::forward<T>(value));
}

/**
 * @brief 创建属性数据的便利函数（默认值）
 * @tparam T 属性值类型
 * @return PropertyData对象
 */
template<typename T>
requires PropertyType<T>
auto makePropertyData() {
    return PropertyData<T>();
}

// ==================================================================================
// 类型推导辅助
// ==================================================================================

/**
 * @brief 属性类型推导辅助结构
 * @tparam T 输入类型
 */
template<typename T>
struct PropertyTypeDeduction {
    using type = std::remove_cv_t<std::remove_reference_t<T>>;
};

/**
 * @brief 属性类型推导辅助别名
 * @tparam T 输入类型
 */
template<typename T>
using PropertyTypeDeductionT = typename PropertyTypeDeduction<T>::type;

/**
 * @brief 从值推导属性类型的便利函数
 * @tparam T 值类型
 * @param value 值
 * @return 推导出的属性类型
 */
template<typename T>
constexpr auto deducePropertyType(const T& value) {
    return PropertyTypeDeductionT<T>{};
}

// ==================================================================================
// 调试和诊断工具
// ==================================================================================

/**
 * @brief 属性系统诊断信息
 */
struct PropertySystemDiagnostics {
    size_t totalProperties = 0;        ///< 总属性数量
    size_t activeObservers = 0;        ///< 活动观察者数量
    size_t activeBindings = 0;         ///< 活动绑定数量
    size_t pendingUpdates = 0;         ///< 待处理更新数量
    
    /**
     * @brief 获取系统诊断信息
     * @return 诊断信息结构
     */
    static PropertySystemDiagnostics getCurrent();
    
    /**
     * @brief 打印诊断信息
     */
    void print() const;
};

/**
 * @brief 获取属性系统版本信息
 * @return 版本字符串
 */
constexpr const char* getPropertySystemVersion() {
    return "2.0.0-modern";
}

/**
 * @brief 获取属性系统构建信息
 * @return 构建信息字符串
 */
constexpr const char* getPropertySystemBuildInfo() {
    return "C++20 Modern Property System - Phase 2 Modularized";
}

// ==================================================================================
// 配置选项
// ==================================================================================

/**
 * @brief 属性系统配置选项
 */
struct PropertySystemConfig {
    bool enableDebugMode = false;           ///< 启用调试模式
    bool enablePerformanceTracking = false; ///< 启用性能跟踪
    bool enableAutomaticCleanup = true;     ///< 启用自动清理
    size_t maxObserversPerProperty = 1000;  ///< 每个属性的最大观察者数量
    
    /**
     * @brief 获取全局配置
     * @return 配置对象的引用
     */
    static PropertySystemConfig& global();
    
    /**
     * @brief 应用配置
     */
    void apply() const;
};

} // namespace PropertySystem

// ==================================================================================
// 全局命名空间便利别名（向后兼容）
// ==================================================================================

// 核心类型
using PropertySystem::PropertyData;
using PropertySystem::UntypedPropertyData;

// 绑定类型
using PropertySystem::PropertyBindingSourceLocation;

// 观察者类型
using PropertySystem::PropertyObserver;
using PropertySystem::PropertyObserverBase;
using PropertySystem::PropertyChangeHandler;

// 工具类型
using PropertySystem::PropertyUpdateGroup;
using PropertySystem::ScopedPropertyUpdateGroup;

// 便利函数
using PropertySystem::makePropertyBinding;
using PropertySystem::makePropertyObserver;
using PropertySystem::makePropertyData;
using PropertySystem::makeUpdateGroup;
using PropertySystem::makeScopedUpdateGroup;

// 诊断和配置
using PropertySystem::PropertySystemDiagnostics;
using PropertySystem::PropertySystemConfig;

// ==================================================================================
// 版本兼容性宏
// ==================================================================================

/**
 * @brief Property系统版本宏
 */
#define PROPERTY_SYSTEM_VERSION_MAJOR 2
#define PROPERTY_SYSTEM_VERSION_MINOR 0
#define PROPERTY_SYSTEM_VERSION_PATCH 0

/**
 * @brief Property系统特性宏
 */
#define PROPERTY_SYSTEM_HAS_MODERN_BINDING 1
#define PROPERTY_SYSTEM_HAS_MODULAR_STRUCTURE 1
#define PROPERTY_SYSTEM_HAS_CONCEPTS 1
#define PROPERTY_SYSTEM_HAS_SMALL_VECTOR 1

/**
 * @brief 检查Property系统版本的宏
 * @param major 主版本号
 * @param minor 次版本号
 * @param patch 补丁版本号
 */
#define PROPERTY_SYSTEM_VERSION_CHECK(major, minor, patch) \
    ((PROPERTY_SYSTEM_VERSION_MAJOR > (major)) || \
     (PROPERTY_SYSTEM_VERSION_MAJOR == (major) && PROPERTY_SYSTEM_VERSION_MINOR > (minor)) || \
     (PROPERTY_SYSTEM_VERSION_MAJOR == (major) && PROPERTY_SYSTEM_VERSION_MINOR == (minor) && \
      PROPERTY_SYSTEM_VERSION_PATCH >= (patch)))

#endif // PROPERTY_SYSTEM_H
