/**
 * @file modern_property_binding.h
 * @brief 现代化的属性绑定实现，移除MetaType依赖
 * 
 * 这个文件提供了简化的属性绑定实现，移除了不必要的MetaType参数，
 * 提高了性能并简化了接口。
 */

#ifndef MODERN_PROPERTY_BINDING_H
#define MODERN_PROPERTY_BINDING_H

#include <memory>
#include <utility>
#include <source_location>
#include "modern_binding_vtable.h"
#include "concepts.h"

// ==================================================================================
// 前向声明
// ==================================================================================

class PropertyBindingPrivate;
class PropertyBindingError;
class UntypedPropertyData;

// ==================================================================================
// 属性绑定源位置
// ==================================================================================

/**
 * @brief 属性绑定的源代码位置信息
 * 
 * 用于调试和错误报告，提供绑定创建时的源代码位置。
 */
struct PropertyBindingSourceLocation {
    const char *fileName = nullptr;
    const char *functionName = nullptr;
    uint32_t line = 0;
    uint32_t column = 0;
    
    PropertyBindingSourceLocation() = default;
    
#ifdef __cpp_lib_source_location
    constexpr PropertyBindingSourceLocation(const std::source_location &cppLocation) {
        fileName = cppLocation.file_name();
        functionName = cppLocation.function_name();
        line = cppLocation.line();
        column = cppLocation.column();
    }
    
    static consteval PropertyBindingSourceLocation
    fromStdSourceLocation(const std::source_location &cppLocation) {
        return cppLocation;
    }
#endif
};

/**
 * @brief 默认绑定位置宏
 */
#ifdef __cpp_lib_source_location
#define PROPERTY_DEFAULT_BINDING_LOCATION \
    PropertyBindingSourceLocation::fromStdSourceLocation(std::source_location::current())
#else
#define PROPERTY_DEFAULT_BINDING_LOCATION PropertyBindingSourceLocation{}
#endif

// ==================================================================================
// 现代化的未类型化属性绑定
// ==================================================================================

/**
 * @brief 现代化的未类型化属性绑定类
 * 
 * 这个类实现了属性之间的绑定关系，移除了MetaType依赖，
 * 提供更简洁和高效的接口。
 */
class ModernUntypedPropertyBinding {
public:
    /**
     * @brief 默认构造函数，创建空绑定
     */
    ModernUntypedPropertyBinding();
    
    /**
     * @brief 从绑定函数表和函数构造
     * @param vtable 绑定函数表指针
     * @param function 可调用对象指针
     * @param location 源代码位置信息
     */
    ModernUntypedPropertyBinding(const ModernBindingFunctionVTable *vtable, 
                                void *function, 
                                const PropertyBindingSourceLocation &location);
    
    /**
     * @brief 从可调用对象构造（模板构造函数）
     * @tparam Functor 可调用对象类型
     * @param f 可调用对象
     * @param location 源代码位置信息
     */
    template<typename Functor>
    ModernUntypedPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location)
        : ModernUntypedPropertyBinding(&modernBindingFunctionVTable<std::remove_reference_t<Functor>>, 
                                      &f, location) {}
    
    /**
     * @brief 移动构造函数
     */
    ModernUntypedPropertyBinding(ModernUntypedPropertyBinding &&other) noexcept;
    
    /**
     * @brief 拷贝构造函数
     */
    ModernUntypedPropertyBinding(const ModernUntypedPropertyBinding &other);
    
    /**
     * @brief 拷贝赋值运算符
     */
    ModernUntypedPropertyBinding &operator=(const ModernUntypedPropertyBinding &other);
    
    /**
     * @brief 移动赋值运算符
     */
    ModernUntypedPropertyBinding &operator=(ModernUntypedPropertyBinding &&other) noexcept;
    
    /**
     * @brief 析构函数
     */
    ~ModernUntypedPropertyBinding();
    
    /**
     * @brief 检查绑定是否为空
     * @return 如果绑定为空则返回true
     */
    bool isNull() const;
    
    /**
     * @brief 获取绑定错误信息
     * @return 绑定错误对象
     */
    PropertyBindingError error() const;
    
    /**
     * @brief 从私有实现构造（内部使用）
     * @param priv 私有实现指针
     */
    explicit ModernUntypedPropertyBinding(PropertyBindingPrivate *priv);

private:
    friend class PropertyBindingData;
    friend class PropertyBindingPrivate;
    template <typename> friend class ModernPropertyBinding;
    
    std::shared_ptr<PropertyBindingPrivate> d;
};

// ==================================================================================
// 现代化的类型化属性绑定
// ==================================================================================

/**
 * @brief 现代化的类型化属性绑定模板类
 * @tparam PropertyType 属性值类型
 * 
 * 这个类提供类型安全的属性绑定，确保绑定函数的返回类型
 * 与属性类型匹配。
 */
template <typename PropertyType>
requires PropertyConcepts::PropertyType<PropertyType>
class ModernPropertyBinding : public ModernUntypedPropertyBinding {
public:
    using value_type = PropertyType;
    
    /**
     * @brief 默认构造函数
     */
    ModernPropertyBinding() = default;
    
    /**
     * @brief 从可调用对象构造属性绑定
     * @tparam Functor 可调用对象类型
     * @param f 绑定函数
     * @param location 源代码位置信息
     */
    template<typename Functor>
    requires PropertyConcepts::PropertyBindingCallable<Functor, PropertyType>
    ModernPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location)
        : ModernUntypedPropertyBinding(&modernBindingFunctionVTable<std::remove_reference_t<Functor>, PropertyType>,
                                      &f, location) {}
    
    /**
     * @brief 从未类型化绑定构造（内部使用）
     * @param binding 未类型化绑定
     */
    explicit ModernPropertyBinding(const ModernUntypedPropertyBinding &binding)
        : ModernUntypedPropertyBinding(binding) {}
};

// ==================================================================================
// 便利函数
// ==================================================================================

/**
 * @brief 创建属性绑定的便利函数
 * @tparam Functor 可调用对象类型
 * @param f 绑定函数
 * @param location 源代码位置信息
 * @return 类型化的属性绑定对象
 */
template <typename Functor>
requires std::invocable<Functor> && PropertyConcepts::PropertyType<std::invoke_result_t<Functor>>
auto makeModernPropertyBinding(Functor &&f, const PropertyBindingSourceLocation &location = PROPERTY_DEFAULT_BINDING_LOCATION) {
    return ModernPropertyBinding<std::invoke_result_t<Functor>>(std::forward<Functor>(f), location);
}

// ==================================================================================
// 全局命名空间别名
// ==================================================================================

// 导出现代化版本到全局命名空间
using ModernUntypedPropertyBinding;
using ModernPropertyBinding;
using makeModernPropertyBinding;

#endif // MODERN_PROPERTY_BINDING_H
